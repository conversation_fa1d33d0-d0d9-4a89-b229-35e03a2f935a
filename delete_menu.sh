#!/bin/bash
# AI论文写作平台 - 菜单删除脚本
# 创建时间: 2025-01-03
# 说明: 删除所有生成的后台权限菜单

echo "=========================================="
echo "AI论文写作平台 - 菜单删除"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

# 确认删除操作
echo "⚠️  警告: 此操作将删除所有已生成的菜单规则！"
echo "包括菜单项、权限节点等"
echo ""
read -p "确认要继续删除吗？(y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始删除AI论文写作平台所有菜单..."

# 写作中心模块菜单
echo ""
echo "📄 删除写作中心模块菜单..."
echo "删除论文类型管理菜单..."
php think menu -c papertype -d 1

echo "删除大纲模板管理菜单..."
php think menu -c outlinetemplate -d 1

echo "删除提示词模板管理菜单..."
php think menu -c prompttemplate -d 1

echo "删除写作任务管理菜单..."
php think menu -c paperproject -d 1

echo "删除草稿箱管理菜单..."
php think menu -c draftbox -d 1

# 降重与查重模块菜单
echo ""
echo "🔄 删除降重与查重模块菜单..."
echo "删除降重任务管理菜单..."
php think menu -c rewritetask -d 1

echo "删除降重结果管理菜单..."
php think menu -c rewriteresult -d 1

echo "删除查重任务管理菜单..."
php think menu -c checktask -d 1

echo "删除查重接口配置菜单..."
php think menu -c checkapi -d 1

# 文档导出模块菜单
echo ""
echo "📁 删除文档导出模块菜单..."
echo "删除导出样式模板菜单..."
php think menu -c documenttemplate -d 1

echo "删除下载记录管理菜单..."
php think menu -c exportrecord -d 1

# 用户中心模块菜单
echo ""
echo "👤 删除用户中心模块菜单..."
echo "删除用户列表菜单..."
php think menu -c user -d 1

echo "删除用户配额管理菜单..."
php think menu -c userquota -d 1

echo "删除VIP套餐管理菜单..."
php think menu -c package -d 1

echo "删除用户积分管理菜单..."
php think menu -c creditslog -d 1

# 收费系统模块菜单
echo ""
echo "💰 删除收费系统模块菜单..."
echo "删除订单管理菜单..."
php think menu -c order -d 1

echo "删除优惠券管理菜单..."
php think menu -c coupon -d 1

echo "删除发票管理菜单..."
php think menu -c invoice -d 1

# 通知与消息模块菜单
echo ""
echo "📬 删除通知与消息模块菜单..."
echo "删除消息模板管理菜单..."
php think menu -c messagetemplate -d 1

echo "删除用户通知记录菜单..."
php think menu -c usernotification -d 1

echo "删除邮件发送记录菜单..."
php think menu -c emaillog -d 1

# 系统设置模块菜单
echo ""
echo "🧠 删除系统设置模块菜单..."
echo "删除AI模型配置菜单..."
php think menu -c aimodel -d 1

echo "删除系统配置管理菜单..."
php think menu -c config -d 1

echo "删除内容风控规则菜单..."
php think menu -c contentfilter -d 1

echo "删除n8n工作流配置菜单..."
php think menu -c n8nworkflow -d 1

echo "删除n8n执行记录菜单..."
php think menu -c n8nexecution -d 1

# 日志统计模块菜单
echo ""
echo "📊 删除日志统计模块菜单..."
echo "删除操作日志菜单..."
php think menu -c operationlog -d 1

echo "删除错误日志菜单..."
php think menu -c errorlog -d 1

echo "删除用户行为统计菜单..."
php think menu -c userstats -d 1

echo "删除系统统计菜单..."
php think menu -c systemstats -d 1

echo "删除AI使用统计菜单..."
php think menu -c aiusagelog -d 1

echo ""
echo "=========================================="
echo "🗑️  所有菜单删除完成！"
echo "=========================================="
echo ""
echo "删除统计:"
echo "- 📄 写作中心模块: 5个菜单已删除"
echo "- 🔄 降重查重模块: 4个菜单已删除"
echo "- 📁 文档导出模块: 2个菜单已删除"
echo "- 👤 用户中心模块: 4个菜单已删除"
echo "- 💰 收费系统模块: 3个菜单已删除"
echo "- 📬 通知消息模块: 3个菜单已删除"
echo "- 🧠 系统设置模块: 5个菜单已删除"
echo "- 📊 日志统计模块: 5个菜单已删除"
echo "- 总计: 31个菜单已删除"
echo ""
echo "注意事项:"
echo "1. 菜单删除后权限节点也会被清理"
echo "2. 如需重新生成菜单，请运行: ./generate_menu.sh"
echo "3. 建议清理浏览器缓存后重新访问后台"
echo "=========================================="
