#!/bin/bash
# AI论文写作平台 - 一键部署脚本
# 创建时间: 2025-01-03
# 说明: 完整部署AI论文写作平台后台管理系统

echo "=========================================="
echo "AI论文写作平台 - 一键部署"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

echo "欢迎使用AI论文写作平台一键部署脚本！"
echo "本脚本将完成以下操作："
echo "1. 生成所有CRUD文件"
echo "2. 生成后台权限菜单"
echo "3. 配置基础数据"
echo ""

# 选择部署方案
echo "请选择部署方案："
echo "1. 核心模块部署（推荐新手，快速原型）"
echo "2. 完整模块部署（完整功能）"
echo "3. 自定义部署（高级用户）"
echo "4. 退出"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "=========================================="
        echo "开始核心模块部署..."
        echo "=========================================="
        
        echo "第一步: 生成核心CRUD..."
        ./generate_core_crud.sh
        
        echo ""
        echo "第二步: 生成核心菜单..."
        ./generate_core_menu.sh
        
        echo ""
        echo "=========================================="
        echo "🎉 核心模块部署完成！"
        echo "=========================================="
        echo ""
        echo "已部署的核心功能："
        echo "✅ 用户管理系统"
        echo "✅ AI模型配置"
        echo "✅ 论文写作功能"
        echo "✅ 降重查重功能"
        echo "✅ VIP套餐系统"
        echo "✅ 订单支付系统"
        echo "✅ 积分管理系统"
        ;;
        
    2)
        echo ""
        echo "=========================================="
        echo "开始完整模块部署..."
        echo "=========================================="
        
        echo "第一步: 生成所有CRUD..."
        ./generate_all_crud.sh
        
        echo ""
        echo "第二步: 生成所有菜单..."
        ./generate_menu.sh
        
        echo ""
        echo "=========================================="
        echo "🎉 完整模块部署完成！"
        echo "=========================================="
        echo ""
        echo "已部署的完整功能："
        echo "✅ 写作中心模块（5个功能）"
        echo "✅ 降重查重模块（4个功能）"
        echo "✅ 文档导出模块（2个功能）"
        echo "✅ 用户中心模块（4个功能）"
        echo "✅ 收费系统模块（3个功能）"
        echo "✅ 通知消息模块（3个功能）"
        echo "✅ 系统设置模块（5个功能）"
        echo "✅ 日志统计模块（5个功能）"
        ;;
        
    3)
        echo ""
        echo "=========================================="
        echo "自定义部署选项"
        echo "=========================================="
        echo ""
        echo "请手动执行以下脚本："
        echo "- ./generate_core_crud.sh     # 生成核心CRUD"
        echo "- ./generate_all_crud.sh      # 生成所有CRUD"
        echo "- ./generate_core_menu.sh     # 生成核心菜单"
        echo "- ./generate_menu.sh          # 生成所有菜单"
        echo "- ./generate_all_menu.sh      # 一键生成所有控制器菜单"
        echo ""
        echo "删除脚本："
        echo "- ./delete_all_crud.sh        # 删除所有CRUD"
        echo "- ./delete_menu.sh            # 删除所有菜单"
        exit 0
        ;;
        
    4)
        echo "部署已取消"
        exit 0
        ;;
        
    *)
        echo "无效选择，部署已取消"
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "部署后配置指南"
echo "=========================================="
echo ""
echo "🔧 必要配置步骤："
echo ""
echo "1. 登录后台管理系统"
echo "   访问: http://your-domain/admin"
echo "   默认账号: admin / 123456"
echo ""
echo "2. 配置字典数据"
echo "   路径: 常规管理 → 系统配置 → 字典配置"
echo "   添加: paper_type, user_type, package_type 等"
echo ""
echo "3. 配置分类数据"
echo "   路径: 常规管理 → 分类管理"
echo "   添加: 论文类型、用户分组、套餐分类等"
echo ""
echo "4. 配置AI模型"
echo "   路径: 系统设置 → AI模型配置"
echo "   添加: OpenAI、Claude、文心一言等模型配置"
echo ""
echo "5. 配置VIP套餐"
echo "   路径: 收费系统 → VIP套餐管理"
echo "   添加: 基础版、专业版、企业版等套餐"
echo ""
echo "6. 测试核心功能"
echo "   - 用户注册登录"
echo "   - 创建写作任务"
echo "   - AI内容生成"
echo "   - 降重查重功能"
echo "   - 套餐购买流程"
echo ""
echo "📚 参考文档："
echo "- AI论文写作平台-CRUD生成命令文档.md"
echo "- README-CRUD生成使用说明.md"
echo "- AI论文写作平台-CRUD生成总结.md"
echo ""
echo "🆘 遇到问题？"
echo "1. 查看详细文档和使用说明"
echo "2. 检查错误日志: runtime/log/"
echo "3. 访问FastAdmin官方文档"
echo "4. 在问答社区寻求帮助"
echo ""
echo "=========================================="
echo "🎉 AI论文写作平台部署完成！"
echo "=========================================="
echo ""
echo "祝您使用愉快！"
