#!/bin/bash
# AI论文写作平台 - 批量生成CRUD脚本
# 创建时间: 2025-01-03
# 说明: 一键生成所有模块的FastAdmin CRUD

echo "=========================================="
echo "AI论文写作平台 - 批量生成CRUD"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

echo "开始生成AI论文写作平台所有CRUD..."

# 写作中心模块
echo ""
echo "📄 生成写作中心模块..."
echo "生成论文类型管理..."
php think crud -t bxw_paper_type -c papertype -u 1

echo "生成大纲模板管理..."
php think crud -t bxw_outline_template -c outlinetemplate \
  --relation=bxw_paper_type \
  --relationforeignkey=paper_type_id \
  --relationprimarykey=id \
  --relationfields=name \
  --relationmode=belongsto \
  --switchsuffix=is_default \
  --switchsuffix=status \
  -u 1

echo "生成提示词模板管理..."
php think crud -t bxw_prompt_template -c prompttemplate \
  --relation=bxw_paper_type \
  --relationforeignkey=paper_type_id \
  --relationprimarykey=id \
  --relationfields=name \
  --relationmode=belongsto \
  --switchsuffix=is_default \
  --switchsuffix=status \
  -u 1

echo "生成写作任务管理..."
php think crud -t bxw_paper_project -c paperproject \
  --relation=bxw_user \
  --relation=bxw_paper_type \
  --relationforeignkey=user_id \
  --relationforeignkey=paper_type_id \
  --relationprimarykey=id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationfields=name \
  --relationmode=belongsto \
  --relationmode=belongsto \
  --switchsuffix=is_draft \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

echo "生成草稿箱管理..."
php think crud -t bxw_paper_project -c draftbox \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=is_draft \
  --sortfield=updatetime \
  -u 1

# 降重与查重模块
echo ""
echo "🔄 生成降重与查重模块..."
echo "生成降重任务管理..."
php think crud -t bxw_rewrite_task -c rewritetask \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

echo "生成降重结果管理..."
php think crud -t bxw_rewrite_result -c rewriteresult \
  --relation=bxw_rewrite_task \
  --relationforeignkey=task_id \
  --relationprimarykey=id \
  --relationfields=title \
  --relationmode=belongsto \
  --switchsuffix=is_selected \
  -u 1

echo "生成查重任务管理..."
php think crud -t bxw_check_task -c checktask \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --filefield=file_path \
  --filefield=report_path \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

echo "生成查重接口配置..."
php think crud -t bxw_check_api -c checkapi \
  --switchsuffix=status \
  --switchsuffix=health_status \
  --setcheckboxsuffix=supported_formats \
  --intdatesuffix=last_health_check \
  --sortfield=priority \
  -u 1

# 文档导出模块
echo ""
echo "📁 生成文档导出模块..."
echo "生成导出样式模板..."
php think crud -t bxw_document_template -c documenttemplate \
  --relation=bxw_paper_type \
  --relationforeignkey=paper_type_id \
  --relationprimarykey=id \
  --relationfields=name \
  --relationmode=belongsto \
  --filefield=template_content \
  --switchsuffix=is_default \
  --switchsuffix=status \
  -u 1

echo "生成下载记录管理..."
php think crud -t bxw_export_record -c exportrecord \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --filefield=file_path \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

# 用户中心模块
echo ""
echo "👤 生成用户中心模块..."
echo "生成用户列表..."
php think crud -t bxw_user -c user \
  --imagefield=avatar \
  --switchsuffix=status \
  --intdatesuffix=vip_expire_time \
  --intdatesuffix=last_login_time \
  --headingfilterfield=user_type \
  --sortfield=createtime \
  --ignorefields=password_hash \
  -u 1

echo "生成用户配额管理..."
php think crud -t bxw_user_quota -c userquota \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=quota_type \
  -u 1

echo "生成VIP套餐管理..."
php think crud -t bxw_package -c package \
  --switchsuffix=is_hot \
  --switchsuffix=status \
  --headingfilterfield=type \
  --sortfield=sort \
  -u 1

echo "生成用户积分管理..."
php think crud -t bxw_credits_log -c creditslog \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=type \
  --sortfield=createtime \
  -u 1

# 收费系统模块
echo ""
echo "💰 生成收费系统模块..."
echo "生成订单管理..."
php think crud -t bxw_order -c order \
  --relation=bxw_user \
  --relation=bxw_package \
  --relationforeignkey=user_id \
  --relationforeignkey=package_id \
  --relationprimarykey=id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationfields=name \
  --relationmode=belongsto \
  --relationmode=belongsto \
  --intdatesuffix=paid_time \
  --intdatesuffix=expired_time \
  --headingfilterfield=payment_status \
  --sortfield=createtime \
  -u 1

echo "生成优惠券管理..."
php think crud -t bxw_coupon -c coupon \
  --intdatesuffix=start_time \
  --intdatesuffix=end_time \
  --switchsuffix=status \
  --headingfilterfield=type \
  -u 1

echo "生成发票管理..."
php think crud -t bxw_invoice -c invoice \
  --relation=bxw_order \
  --relation=bxw_user \
  --relationforeignkey=order_id \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationprimarykey=id \
  --relationfields=order_no \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --relationmode=belongsto \
  --filefield=invoice_file_path \
  --intdatesuffix=apply_time \
  --intdatesuffix=issue_time \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

echo ""
echo "✅ 第一批核心模块生成完成！"
echo "继续生成剩余模块..."

# 通知与消息模块
echo ""
echo "📬 生成通知与消息模块..."
echo "生成消息模板管理..."
php think crud -t bxw_message_template -c messagetemplate \
  --editorclass=content \
  --switchsuffix=status \
  --headingfilterfield=type \
  -u 1

echo "生成用户通知记录..."
php think crud -t bxw_user_notification -c usernotification \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --switchsuffix=is_read \
  --intdatesuffix=read_time \
  --headingfilterfield=type \
  --sortfield=createtime \
  -u 1

echo "生成邮件发送记录..."
php think crud -t bxw_email_log -c emaillog \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --intdatesuffix=sent_time \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

# 系统设置模块
echo ""
echo "🧠 生成系统设置模块..."
echo "生成AI模型配置..."
php think crud -t bxw_ai_model -c aimodel \
  --switchsuffix=status \
  --switchsuffix=health_status \
  --intdatesuffix=last_health_check \
  --headingfilterfield=provider \
  --sortfield=priority \
  --ignorefields=api_key \
  -u 1

echo "生成系统配置管理..."
php think crud -t bxw_config -c config \
  --headingfilterfield=group \
  --sortfield=sort \
  -u 1

echo "生成内容风控规则..."
php think crud -t bxw_content_filter -c contentfilter \
  --switchsuffix=status \
  --headingfilterfield=type \
  --headingfilterfield=severity \
  -u 1

echo "生成n8n工作流配置..."
php think crud -t bxw_n8n_workflow -c n8nworkflow \
  --switchsuffix=is_active \
  --switchsuffix=status \
  --headingfilterfield=type \
  -u 1

echo "生成n8n执行记录..."
php think crud -t bxw_n8n_execution -c n8nexecution \
  --relation=bxw_n8n_workflow \
  --relation=bxw_user \
  --relationforeignkey=workflow_id \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationprimarykey=id \
  --relationfields=name \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --relationmode=belongsto \
  --intdatesuffix=start_time \
  --intdatesuffix=end_time \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

# 日志统计模块
echo ""
echo "📊 生成日志统计模块..."
echo "生成操作日志..."
php think crud -t bxw_operation_log -c operationlog \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=module \
  --headingfilterfield=action \
  --sortfield=createtime \
  -u 1

echo "生成错误日志..."
php think crud -t bxw_error_log -c errorlog \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --headingfilterfield=level \
  --headingfilterfield=category \
  --sortfield=createtime \
  -u 1

echo "生成用户行为统计..."
php think crud -t bxw_user_stats -c userstats \
  --relation=bxw_user \
  --relationforeignkey=user_id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationmode=belongsto \
  --intdatesuffix=date \
  --sortfield=date \
  -u 1

echo "生成系统统计..."
php think crud -t bxw_system_stats -c systemstats \
  --intdatesuffix=date \
  --sortfield=date \
  -u 1

echo "生成AI使用统计..."
php think crud -t bxw_ai_usage_log -c aiusagelog \
  --relation=bxw_user \
  --relation=bxw_ai_model \
  --relationforeignkey=user_id \
  --relationforeignkey=ai_model_id \
  --relationprimarykey=id \
  --relationprimarykey=id \
  --relationfields=username,nickname \
  --relationfields=name \
  --relationmode=belongsto \
  --relationmode=belongsto \
  --headingfilterfield=task_type \
  --headingfilterfield=status \
  --sortfield=createtime \
  -u 1

echo ""
echo "=========================================="
echo "🎉 所有CRUD生成完成！"
echo "=========================================="
echo ""
echo "生成统计:"
echo "- 📄 写作中心模块: 5个CRUD"
echo "- 🔄 降重查重模块: 4个CRUD"
echo "- 📁 文档导出模块: 2个CRUD"
echo "- 👤 用户中心模块: 4个CRUD"
echo "- 💰 收费系统模块: 3个CRUD"
echo "- 📬 通知消息模块: 3个CRUD"
echo "- 🧠 系统设置模块: 5个CRUD"
echo "- 📊 日志统计模块: 5个CRUD"
echo "- 总计: 31个CRUD"
echo ""
echo "下一步操作:"
echo "1. 登录后台查看生成的菜单"
echo "2. 配置字典数据（分类、状态等）"
echo "3. 调整字段显示和验证规则"
echo "4. 测试各模块功能"
echo ""
echo "如需删除重新生成，请运行: ./delete_all_crud.sh"
echo "如需单独生成菜单，请运行: ./generate_menu.sh"
echo "=========================================="
