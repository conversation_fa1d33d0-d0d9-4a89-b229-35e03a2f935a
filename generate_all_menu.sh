#!/bin/bash
# AI论文写作平台 - 一键生成所有菜单脚本
# 创建时间: 2025-01-03
# 说明: 使用FastAdmin的all-controller参数一键生成所有控制器菜单

echo "=========================================="
echo "AI论文写作平台 - 一键生成所有菜单"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

# 确认操作
echo "⚠️  注意: 此操作将为所有控制器生成菜单！"
echo "包括系统自带的控制器和自定义控制器"
echo "执行前请确保已备份数据库"
echo ""
read -p "确认要继续生成吗？(y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始一键生成所有控制器菜单..."
echo "这可能需要一些时间，请耐心等待..."

# 使用FastAdmin的all-controller参数一键生成所有菜单
php think menu -c all-controller

echo ""
echo "=========================================="
echo "🎉 所有控制器菜单生成完成！"
echo "=========================================="
echo ""
echo "生成说明:"
echo "✅ 已为所有存在的控制器生成菜单"
echo "✅ 包括系统控制器和自定义控制器"
echo "✅ 自动创建权限节点和菜单规则"
echo ""
echo "下一步操作:"
echo "1. 登录后台查看生成的菜单结构"
echo "2. 删除不需要的菜单项"
echo "3. 调整菜单显示名称和图标"
echo "4. 设置菜单权限和角色分配"
echo "5. 整理菜单层级结构"
echo ""
echo "菜单管理路径: 后台 → 权限管理 → 菜单规则"
echo ""
echo "注意事项:"
echo "- 可能会生成很多不需要的菜单，请手动清理"
echo "- 建议先使用 generate_core_menu.sh 生成核心菜单"
echo "- 如需删除所有菜单，请运行: ./delete_menu.sh"
echo "=========================================="
