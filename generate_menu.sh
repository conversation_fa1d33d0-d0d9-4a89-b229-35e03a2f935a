#!/bin/bash
# AI论文写作平台 - 菜单生成脚本
# 创建时间: 2025-01-03
# 说明: 为所有控制器生成后台权限菜单

echo "=========================================="
echo "AI论文写作平台 - 菜单生成"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

echo "开始为AI论文写作平台生成后台权限菜单..."
echo "注意: 请确保对应的控制器已经通过CRUD生成"

# 写作中心模块菜单
echo ""
echo "📄 生成写作中心模块菜单..."
echo "生成论文类型管理菜单..."
php think menu -c papertype

echo "生成大纲模板管理菜单..."
php think menu -c outlinetemplate

echo "生成提示词模板管理菜单..."
php think menu -c prompttemplate

echo "生成写作任务管理菜单..."
php think menu -c paperproject

echo "生成草稿箱管理菜单..."
php think menu -c draftbox

# 降重与查重模块菜单
echo ""
echo "🔄 生成降重与查重模块菜单..."
echo "生成降重任务管理菜单..."
php think menu -c rewritetask

echo "生成降重结果管理菜单..."
php think menu -c rewriteresult

echo "生成查重任务管理菜单..."
php think menu -c checktask

echo "生成查重接口配置菜单..."
php think menu -c checkapi

# 文档导出模块菜单
echo ""
echo "📁 生成文档导出模块菜单..."
echo "生成导出样式模板菜单..."
php think menu -c documenttemplate

echo "生成下载记录管理菜单..."
php think menu -c exportrecord

# 用户中心模块菜单
echo ""
echo "👤 生成用户中心模块菜单..."
echo "生成用户列表菜单..."
php think menu -c user

echo "生成用户配额管理菜单..."
php think menu -c userquota

echo "生成VIP套餐管理菜单..."
php think menu -c package

echo "生成用户积分管理菜单..."
php think menu -c creditslog

# 收费系统模块菜单
echo ""
echo "💰 生成收费系统模块菜单..."
echo "生成订单管理菜单..."
php think menu -c order

echo "生成优惠券管理菜单..."
php think menu -c coupon

echo "生成发票管理菜单..."
php think menu -c invoice

# 通知与消息模块菜单
echo ""
echo "📬 生成通知与消息模块菜单..."
echo "生成消息模板管理菜单..."
php think menu -c messagetemplate

echo "生成用户通知记录菜单..."
php think menu -c usernotification

echo "生成邮件发送记录菜单..."
php think menu -c emaillog

# 系统设置模块菜单
echo ""
echo "🧠 生成系统设置模块菜单..."
echo "生成AI模型配置菜单..."
php think menu -c aimodel

echo "生成系统配置管理菜单..."
php think menu -c config

echo "生成内容风控规则菜单..."
php think menu -c contentfilter

echo "生成n8n工作流配置菜单..."
php think menu -c n8nworkflow

echo "生成n8n执行记录菜单..."
php think menu -c n8nexecution

# 日志统计模块菜单
echo ""
echo "📊 生成日志统计模块菜单..."
echo "生成操作日志菜单..."
php think menu -c operationlog

echo "生成错误日志菜单..."
php think menu -c errorlog

echo "生成用户行为统计菜单..."
php think menu -c userstats

echo "生成系统统计菜单..."
php think menu -c systemstats

echo "生成AI使用统计菜单..."
php think menu -c aiusagelog

echo ""
echo "=========================================="
echo "🎉 所有菜单生成完成！"
echo "=========================================="
echo ""
echo "菜单生成统计:"
echo "- 📄 写作中心模块: 5个菜单"
echo "- 🔄 降重查重模块: 4个菜单"
echo "- 📁 文档导出模块: 2个菜单"
echo "- 👤 用户中心模块: 4个菜单"
echo "- 💰 收费系统模块: 3个菜单"
echo "- 📬 通知消息模块: 3个菜单"
echo "- 🧠 系统设置模块: 5个菜单"
echo "- 📊 日志统计模块: 5个菜单"
echo "- 总计: 31个菜单"
echo ""
echo "下一步操作:"
echo "1. 登录后台查看生成的菜单结构"
echo "2. 调整菜单显示名称和图标"
echo "3. 设置菜单权限和角色分配"
echo "4. 测试菜单访问权限"
echo ""
echo "菜单管理路径: 后台 → 权限管理 → 菜单规则"
echo "如需删除菜单，请运行: ./delete_menu.sh"
echo "=========================================="
