-- =====================================================
-- AI论文写作平台数据库设计 (ThinkAdmin + n8n 架构) - MySQL版本
-- 表前缀: bxw_
-- 创建时间: 2025-01-03
-- 说明: 基于ThinkAdmin框架的AI论文写作平台数据库结构 (MySQL适配版)
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 用户相关表
-- =====================================================

-- 用户基础信息表
DROP TABLE IF EXISTS `bxw_user`;
CREATE TABLE `bxw_user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1正常/0禁用)',
  `user_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户类型(1普通/2VIP/3企业)',
  `credits` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '积分余额',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP到期时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_user_username` (`username`),
  UNIQUE KEY `idx_bxw_user_email` (`email`),
  KEY `idx_bxw_user_status` (`status`),
  KEY `idx_bxw_user_type` (`user_type`),
  CONSTRAINT `chk_user_status` CHECK (`status` IN (0, 1)),
  CONSTRAINT `chk_user_type` CHECK (`user_type` IN (1, 2, 3))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 用户配额限制表
DROP TABLE IF EXISTS `bxw_user_quota`;
CREATE TABLE `bxw_user_quota` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配额ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `quota_type` varchar(50) NOT NULL COMMENT '配额类型(writing/rewrite/check)',
  `daily_limit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日限额',
  `monthly_limit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '月限额',
  `daily_used` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日已用',
  `monthly_used` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '月已用',
  `last_reset_date` date NOT NULL COMMENT '最后重置日期',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_user_quota_user_type` (`user_id`, `quota_type`),
  KEY `idx_bxw_user_quota_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户配额限制表';

-- =====================================================
-- 2. 论文写作相关表
-- =====================================================

-- 论文类型表
DROP TABLE IF EXISTS `bxw_paper_type`;
CREATE TABLE `bxw_paper_type` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `name` varchar(100) NOT NULL COMMENT '类型名称',
  `description` text COMMENT '类型描述',
  `word_count_min` int(10) unsigned NOT NULL DEFAULT '6000' COMMENT '最小字数',
  `word_count_max` int(10) unsigned NOT NULL DEFAULT '30000' COMMENT '最大字数',
  `outline_template_id` int(10) unsigned DEFAULT NULL COMMENT '默认大纲模板ID',
  `prompt_template_id` int(10) unsigned DEFAULT NULL COMMENT '默认提示词模板ID',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_paper_type_status` (`status`),
  KEY `idx_bxw_paper_type_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论文类型表';

-- 大纲模板表
DROP TABLE IF EXISTS `bxw_outline_template`;
CREATE TABLE `bxw_outline_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '论文类型ID',
  `template_content` longtext NOT NULL COMMENT '模板内容',
  `description` text COMMENT '模板描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认(1是/0否)',
  `usage_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_outline_template_paper_type_id` (`paper_type_id`),
  KEY `idx_bxw_outline_template_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大纲模板表';

-- 提示词模板表
DROP TABLE IF EXISTS `bxw_prompt_template`;
CREATE TABLE `bxw_prompt_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(50) NOT NULL COMMENT '模板类型(outline/writing/rewrite)',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '论文类型ID',
  `prompt_content` longtext NOT NULL COMMENT '提示词内容',
  `variables` text COMMENT '变量配置(JSON)',
  `ai_model` varchar(50) DEFAULT NULL COMMENT '适用AI模型',
  `description` text COMMENT '模板描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认(1是/0否)',
  `usage_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_prompt_template_type` (`type`),
  KEY `idx_bxw_prompt_template_paper_type_id` (`paper_type_id`),
  KEY `idx_bxw_prompt_template_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词模板表';

-- 论文项目表
DROP TABLE IF EXISTS `bxw_paper_project`;
CREATE TABLE `bxw_paper_project` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '论文标题',
  `paper_type_id` int(10) unsigned NOT NULL COMMENT '论文类型ID',
  `subject` varchar(100) DEFAULT NULL COMMENT '学科专业',
  `keywords` varchar(500) DEFAULT NULL COMMENT '关键词',
  `requirements` text COMMENT '写作要求',
  `target_word_count` int(10) unsigned NOT NULL DEFAULT '10000' COMMENT '目标字数',
  `writing_style` varchar(50) NOT NULL DEFAULT 'academic' COMMENT '写作风格',
  `outline_content` longtext COMMENT '大纲内容',
  `outline_version` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '大纲版本',
  `content` longtext COMMENT '论文内容',
  `current_word_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '当前字数',
  `status` enum('draft','outline_generating','writing','completed','failed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `is_draft` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否草稿(1是/0否)',
  `draft_version` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '草稿版本',
  `parent_id` int(10) unsigned DEFAULT NULL COMMENT '父项目ID',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `progress` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '进度(0-100)',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_paper_project_user_id` (`user_id`),
  KEY `idx_bxw_paper_project_paper_type_id` (`paper_type_id`),
  KEY `idx_bxw_paper_project_status` (`status`),
  KEY `idx_bxw_paper_project_createtime` (`createtime`),
  KEY `idx_bxw_paper_project_is_draft` (`is_draft`),
  KEY `idx_bxw_paper_project_parent_id` (`parent_id`),
  KEY `idx_user_task_status_time` (`user_id`, `status`, `createtime`),
  KEY `idx_user_draft_version` (`user_id`, `is_draft`, `draft_version`),
  CONSTRAINT `chk_progress` CHECK (`progress` >= 0 AND `progress` <= 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论文项目表';

-- 论文章节表
DROP TABLE IF EXISTS `bxw_paper_section`;
CREATE TABLE `bxw_paper_section` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '章节ID',
  `project_id` int(10) unsigned NOT NULL COMMENT '项目ID',
  `section_title` varchar(255) NOT NULL COMMENT '章节标题',
  `section_level` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '章节级别',
  `section_order` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '章节顺序',
  `content` longtext COMMENT '章节内容',
  `word_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '字数',
  `status` enum('pending','generating','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `ai_model_used` varchar(50) DEFAULT NULL COMMENT '使用的AI模型',
  `generation_time` datetime DEFAULT NULL COMMENT '生成时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_paper_section_project_id` (`project_id`),
  KEY `idx_bxw_paper_section_status` (`status`),
  KEY `idx_bxw_paper_section_order` (`section_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论文章节表';

-- =====================================================
-- 3. AI模型配置相关表
-- =====================================================

-- AI模型配置表
DROP TABLE IF EXISTS `bxw_ai_model`;
CREATE TABLE `bxw_ai_model` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `provider` varchar(50) NOT NULL COMMENT '服务商(openai/baidu/aliyun)',
  `model_code` varchar(100) NOT NULL COMMENT '模型代码',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `max_tokens` int(10) unsigned NOT NULL DEFAULT '4000' COMMENT '最大令牌数',
  `temperature` decimal(3,2) NOT NULL DEFAULT '0.70' COMMENT '温度参数',
  `top_p` decimal(3,2) NOT NULL DEFAULT '1.00' COMMENT 'Top P参数',
  `frequency_penalty` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '存在惩罚',
  `cost_per_1k_tokens` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT '千令牌成本',
  `priority` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优先级',
  `rate_limit_per_minute` int(10) unsigned NOT NULL DEFAULT '60' COMMENT '每分钟限制',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `health_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '健康状态(1正常/0异常)',
  `last_health_check` datetime DEFAULT NULL COMMENT '最后健康检查时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_ai_model_provider_model` (`provider`, `model_code`),
  KEY `idx_bxw_ai_model_status` (`status`),
  KEY `idx_bxw_ai_model_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表';

-- AI模型使用统计表
DROP TABLE IF EXISTS `bxw_ai_usage_log`;
CREATE TABLE `bxw_ai_usage_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `ai_model_id` int(10) unsigned NOT NULL COMMENT 'AI模型ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型(outline/writing/rewrite)',
  `task_id` int(10) unsigned DEFAULT NULL COMMENT '任务ID',
  `prompt_tokens` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提示令牌数',
  `completion_tokens` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成令牌数',
  `total_tokens` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总令牌数',
  `cost` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT '调用成本',
  `response_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '响应时间(毫秒)',
  `status` enum('success','failed') NOT NULL DEFAULT 'success' COMMENT '调用状态',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_ai_usage_log_user_id` (`user_id`),
  KEY `idx_bxw_ai_usage_log_ai_model_id` (`ai_model_id`),
  KEY `idx_bxw_ai_usage_log_task_type` (`task_type`),
  KEY `idx_bxw_ai_usage_log_createtime` (`createtime`),
  KEY `idx_ai_usage_model_time` (`ai_model_id`, `createtime`),
  KEY `idx_ai_usage_user_task` (`user_id`, `task_type`, `createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型使用统计表';

-- =====================================================
-- 4. 语义降重相关表
-- =====================================================

-- 降重任务表
DROP TABLE IF EXISTS `bxw_rewrite_task`;
CREATE TABLE `bxw_rewrite_task` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '任务标题',
  `original_text` longtext NOT NULL COMMENT '原始文本',
  `original_word_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原始字数',
  `rewrite_mode` varchar(50) NOT NULL DEFAULT 'standard' COMMENT '降重模式(standard/deep/creative)',
  `target_similarity` decimal(5,2) NOT NULL DEFAULT '30.00' COMMENT '目标相似度(%)',
  `ai_model_id` int(10) unsigned DEFAULT NULL COMMENT 'AI模型ID',
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `progress` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '进度(0-100)',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_rewrite_task_user_id` (`user_id`),
  KEY `idx_bxw_rewrite_task_status` (`status`),
  KEY `idx_bxw_rewrite_task_createtime` (`createtime`),
  CONSTRAINT `chk_rewrite_progress` CHECK (`progress` >= 0 AND `progress` <= 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='降重任务表';

-- 降重结果表
DROP TABLE IF EXISTS `bxw_rewrite_result`;
CREATE TABLE `bxw_rewrite_result` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` int(10) unsigned NOT NULL COMMENT '任务ID',
  `ai_model_id` int(10) unsigned NOT NULL COMMENT 'AI模型ID',
  `rewritten_text` longtext NOT NULL COMMENT '降重后文本',
  `rewritten_word_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '降重后字数',
  `similarity_score` decimal(5,2) DEFAULT NULL COMMENT '相似度分数',
  `quality_score` decimal(5,2) DEFAULT NULL COMMENT '质量分数',
  `processing_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '处理时间(秒)',
  `is_selected` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否选中(1是/0否)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_rewrite_result_task_id` (`task_id`),
  KEY `idx_bxw_rewrite_result_ai_model_id` (`ai_model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='降重结果表';

-- =====================================================
-- 5. 查重相关表
-- =====================================================

-- 查重接口配置表
DROP TABLE IF EXISTS `bxw_check_api`;
CREATE TABLE `bxw_check_api` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '接口ID',
  `name` varchar(100) NOT NULL COMMENT '接口名称',
  `provider` varchar(50) NOT NULL COMMENT '服务商(weipu/cnki/wanfang)',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `cost_per_check` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '每次查重成本',
  `max_word_count` int(10) unsigned NOT NULL DEFAULT '50000' COMMENT '最大字数限制',
  `supported_formats` varchar(255) NOT NULL DEFAULT 'txt,doc,docx,pdf' COMMENT '支持格式',
  `priority` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优先级',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `health_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '健康状态(1正常/0异常)',
  `last_health_check` datetime DEFAULT NULL COMMENT '最后健康检查时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_check_api_provider` (`provider`),
  KEY `idx_bxw_check_api_status` (`status`),
  KEY `idx_bxw_check_api_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查重接口配置表';

-- 查重任务表
DROP TABLE IF EXISTS `bxw_check_task`;
CREATE TABLE `bxw_check_task` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '任务标题',
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `word_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '字数',
  `check_api_id` int(10) unsigned NOT NULL COMMENT '查重接口ID',
  `external_task_id` varchar(100) DEFAULT NULL COMMENT '外部任务ID',
  `status` enum('pending','checking','completed','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `similarity_rate` decimal(5,2) DEFAULT NULL COMMENT '相似度(%)',
  `report_url` varchar(255) DEFAULT NULL COMMENT '报告URL',
  `report_path` varchar(255) DEFAULT NULL COMMENT '报告文件路径',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `cost` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '查重成本',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_check_task_user_id` (`user_id`),
  KEY `idx_bxw_check_task_check_api_id` (`check_api_id`),
  KEY `idx_bxw_check_task_status` (`status`),
  KEY `idx_bxw_check_task_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查重任务表';

-- 查重报告详情表
DROP TABLE IF EXISTS `bxw_check_detail`;
CREATE TABLE `bxw_check_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '详情ID',
  `task_id` int(10) unsigned NOT NULL COMMENT '任务ID',
  `paragraph_index` int(10) unsigned NOT NULL COMMENT '段落索引',
  `original_text` text NOT NULL COMMENT '原始文本',
  `similarity_rate` decimal(5,2) NOT NULL COMMENT '相似度(%)',
  `matched_sources` text COMMENT '匹配来源(JSON)',
  `suggestions` text COMMENT '修改建议',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_check_detail_task_id` (`task_id`),
  KEY `idx_bxw_check_detail_similarity_rate` (`similarity_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查重报告详情表';

-- =====================================================
-- 6. 文档导出相关表
-- =====================================================

-- 文档模板表
DROP TABLE IF EXISTS `bxw_document_template`;
CREATE TABLE `bxw_document_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(50) NOT NULL COMMENT '模板类型(docx/pdf)',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '论文类型ID',
  `template_content` longtext NOT NULL COMMENT '模板内容',
  `style_config` text COMMENT '样式配置(JSON)',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认(1是/0否)',
  `usage_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_document_template_type` (`type`),
  KEY `idx_bxw_document_template_paper_type_id` (`paper_type_id`),
  KEY `idx_bxw_document_template_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档模板表';

-- 文档导出记录表
DROP TABLE IF EXISTS `bxw_export_record`;
CREATE TABLE `bxw_export_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型(paper/rewrite)',
  `source_id` int(10) unsigned NOT NULL COMMENT '来源ID',
  `export_format` varchar(20) NOT NULL COMMENT '导出格式(docx/pdf)',
  `template_id` int(10) unsigned DEFAULT NULL COMMENT '模板ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `file_size` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `download_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_export_record_user_id` (`user_id`),
  KEY `idx_bxw_export_record_source_type_id` (`source_type`, `source_id`),
  KEY `idx_bxw_export_record_status` (`status`),
  KEY `idx_bxw_export_record_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档导出记录表';

-- 发票管理表
DROP TABLE IF EXISTS `bxw_invoice`;
CREATE TABLE `bxw_invoice` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '发票ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `invoice_type` enum('personal','company') NOT NULL DEFAULT 'personal' COMMENT '发票类型',
  `invoice_title` varchar(200) NOT NULL COMMENT '发票抬头',
  `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
  `invoice_content` varchar(500) DEFAULT NULL COMMENT '发票内容',
  `invoice_amount` decimal(10,2) NOT NULL COMMENT '发票金额',
  `status` enum('pending','processing','issued','failed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `invoice_code` varchar(50) DEFAULT NULL COMMENT '发票代码',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `invoice_url` varchar(255) DEFAULT NULL COMMENT '发票链接',
  `invoice_file_path` varchar(255) DEFAULT NULL COMMENT '发票文件路径',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `issue_time` datetime DEFAULT NULL COMMENT '开具时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_invoice_order_id` (`order_id`),
  KEY `idx_bxw_invoice_user_id` (`user_id`),
  KEY `idx_bxw_invoice_status` (`status`),
  KEY `idx_bxw_invoice_apply_time` (`apply_time`),
  CONSTRAINT `chk_invoice_amount` CHECK (`invoice_amount` > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发票管理表';

-- =====================================================
-- 7. 订单支付相关表
-- =====================================================

-- 套餐配置表
DROP TABLE IF EXISTS `bxw_package`;
CREATE TABLE `bxw_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
  `name` varchar(100) NOT NULL COMMENT '套餐名称',
  `type` enum('credits','vip','combo') NOT NULL COMMENT '套餐类型',
  `description` text COMMENT '套餐描述',
  `credits` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '赠送积分',
  `vip_days` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'VIP天数',
  `writing_quota` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '写作配额',
  `rewrite_quota` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '降重配额',
  `check_quota` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查重配额',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售价',
  `discount_rate` decimal(5,2) NOT NULL DEFAULT '100.00' COMMENT '折扣率(%)',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热门(1是/0否)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_package_type` (`type`),
  KEY `idx_bxw_package_status` (`status`),
  KEY `idx_bxw_package_sort` (`sort`),
  CONSTRAINT `chk_package_prices` CHECK (`original_price` >= 0 AND `sale_price` >= 0 AND `sale_price` <= `original_price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐配置表';

-- 订单表
DROP TABLE IF EXISTS `bxw_order`;
CREATE TABLE `bxw_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `package_id` int(10) unsigned NOT NULL COMMENT '套餐ID',
  `package_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `final_price` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` enum('wechat','alipay','bank','balance') DEFAULT NULL COMMENT '支付方式',
  `payment_status` enum('pending','paid','failed','refunded','cancelled') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '交易号',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `expired_time` datetime NOT NULL COMMENT '过期时间',
  `coupon_id` int(10) unsigned DEFAULT NULL COMMENT '优惠券ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_order_order_no` (`order_no`),
  KEY `idx_bxw_order_user_id` (`user_id`),
  KEY `idx_bxw_order_package_id` (`package_id`),
  KEY `idx_bxw_order_payment_status` (`payment_status`),
  KEY `idx_bxw_order_createtime` (`createtime`),
  CONSTRAINT `chk_order_prices` CHECK (`original_price` >= 0 AND `final_price` >= 0 AND `discount_amount` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 优惠券表
DROP TABLE IF EXISTS `bxw_coupon`;
CREATE TABLE `bxw_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) NOT NULL COMMENT '优惠券代码',
  `type` enum('percent','fixed') NOT NULL COMMENT '优惠类型(percent百分比/fixed固定金额)',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最小使用金额',
  `max_discount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最大优惠金额',
  `total_quantity` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总数量',
  `used_quantity` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `per_user_limit` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '每用户限制',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `applicable_packages` varchar(500) DEFAULT NULL COMMENT '适用套餐(JSON)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_coupon_code` (`code`),
  KEY `idx_bxw_coupon_status` (`status`),
  KEY `idx_bxw_coupon_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券表';

-- 用户优惠券表
DROP TABLE IF EXISTS `bxw_user_coupon`;
CREATE TABLE `bxw_user_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `coupon_id` int(10) unsigned NOT NULL COMMENT '优惠券ID',
  `order_id` int(10) unsigned DEFAULT NULL COMMENT '订单ID',
  `status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '状态',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_user_coupon_user_id` (`user_id`),
  KEY `idx_bxw_user_coupon_coupon_id` (`coupon_id`),
  KEY `idx_bxw_user_coupon_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券表';

-- 积分变动记录表
DROP TABLE IF EXISTS `bxw_credits_log`;
CREATE TABLE `bxw_credits_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '变动类型(recharge/consume/refund)',
  `amount` int(11) NOT NULL COMMENT '变动数量(正数增加/负数减少)',
  `balance_before` int(10) unsigned NOT NULL COMMENT '变动前余额',
  `balance_after` int(10) unsigned NOT NULL COMMENT '变动后余额',
  `related_id` int(10) unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型(order/task/refund)',
  `description` varchar(255) NOT NULL COMMENT '变动说明',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_credits_log_user_id` (`user_id`),
  KEY `idx_bxw_credits_log_type` (`type`),
  KEY `idx_bxw_credits_log_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分变动记录表';

-- =====================================================
-- 8. 系统配置相关表
-- =====================================================

-- 系统配置表
DROP TABLE IF EXISTS `bxw_config`;
CREATE TABLE `bxw_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `group` varchar(50) NOT NULL COMMENT '配置分组',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `title` varchar(100) NOT NULL COMMENT '显示标题',
  `value` text COMMENT '配置值',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '值类型(string/number/boolean)',
  `options` text COMMENT '选项配置(JSON)',
  `description` varchar(500) DEFAULT NULL COMMENT '配置说明',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_config_group_name` (`group`, `name`),
  KEY `idx_bxw_config_group` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 内容风控规则表
DROP TABLE IF EXISTS `bxw_content_filter`;
CREATE TABLE `bxw_content_filter` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` varchar(100) NOT NULL COMMENT '规则名称',
  `type` varchar(50) NOT NULL COMMENT '规则类型(keyword/regex/ai)',
  `pattern` text NOT NULL COMMENT '匹配模式',
  `action` varchar(20) NOT NULL DEFAULT 'block' COMMENT '处理动作(block/replace/warn)',
  `replacement` varchar(500) DEFAULT NULL COMMENT '替换内容',
  `severity` varchar(20) NOT NULL DEFAULT 'medium' COMMENT '严重程度(low/medium/high)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_content_filter_type` (`type`),
  KEY `idx_bxw_content_filter_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容风控规则表';

-- =====================================================
-- 9. 通知相关表
-- =====================================================

-- 消息模板表
DROP TABLE IF EXISTS `bxw_message_template`;
CREATE TABLE `bxw_message_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `code` varchar(50) NOT NULL COMMENT '模板代码',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(20) NOT NULL COMMENT '消息类型(system/email/sms)',
  `subject` varchar(255) DEFAULT NULL COMMENT '消息主题',
  `content` text NOT NULL COMMENT '消息内容',
  `variables` text COMMENT '变量配置(JSON)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_message_template_code` (`code`),
  KEY `idx_bxw_message_template_type` (`type`),
  KEY `idx_bxw_message_template_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息模板表';

-- 用户通知表
DROP TABLE IF EXISTS `bxw_user_notification`;
CREATE TABLE `bxw_user_notification` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '通知类型(task/payment/system)',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `related_id` int(10) unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型(task/order/system)',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读(1是/0否)',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_user_notification_user_id` (`user_id`),
  KEY `idx_bxw_user_notification_type` (`type`),
  KEY `idx_bxw_user_notification_is_read` (`is_read`),
  KEY `idx_bxw_user_notification_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知表';

-- 邮件发送记录表
DROP TABLE IF EXISTS `bxw_email_log`;
CREATE TABLE `bxw_email_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '用户ID',
  `template_id` int(10) unsigned DEFAULT NULL COMMENT '模板ID',
  `to_email` varchar(255) NOT NULL COMMENT '收件邮箱',
  `subject` varchar(255) NOT NULL COMMENT '邮件主题',
  `content` text NOT NULL COMMENT '邮件内容',
  `status` enum('pending','sent','failed') NOT NULL DEFAULT 'pending' COMMENT '发送状态',
  `error_message` text COMMENT '错误信息',
  `sent_time` datetime DEFAULT NULL COMMENT '发送时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_email_log_user_id` (`user_id`),
  KEY `idx_bxw_email_log_status` (`status`),
  KEY `idx_bxw_email_log_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件发送记录表';

-- =====================================================
-- 10. n8n集成相关表
-- =====================================================

-- n8n工作流配置表
DROP TABLE IF EXISTS `bxw_n8n_workflow`;
CREATE TABLE `bxw_n8n_workflow` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '工作流ID',
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `type` varchar(50) NOT NULL COMMENT '工作流类型(outline/writing/rewrite/check/export)',
  `n8n_workflow_id` varchar(100) NOT NULL COMMENT 'n8n平台工作流ID',
  `webhook_url` varchar(255) DEFAULT NULL COMMENT 'Webhook地址',
  `description` text COMMENT '工作流描述',
  `config` text COMMENT '配置参数(JSON)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活(1是/0否)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1启用/0禁用)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_n8n_workflow_n8n_id` (`n8n_workflow_id`),
  KEY `idx_bxw_n8n_workflow_type` (`type`),
  KEY `idx_bxw_n8n_workflow_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='n8n工作流配置表';

-- n8n执行记录表
DROP TABLE IF EXISTS `bxw_n8n_execution`;
CREATE TABLE `bxw_n8n_execution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '执行ID',
  `workflow_id` int(10) unsigned NOT NULL COMMENT '工作流ID',
  `n8n_execution_id` varchar(100) NOT NULL COMMENT 'n8n平台执行ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型(outline/writing/rewrite/check/export)',
  `task_id` int(10) unsigned NOT NULL COMMENT '任务ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `input_data` text COMMENT '输入数据(JSON)',
  `output_data` text COMMENT '输出数据(JSON)',
  `status` enum('running','success','failed','cancelled') NOT NULL DEFAULT 'running' COMMENT '执行状态',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(10) unsigned DEFAULT NULL COMMENT '执行时长(秒)',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_n8n_execution_n8n_id` (`n8n_execution_id`),
  KEY `idx_bxw_n8n_execution_workflow_id` (`workflow_id`),
  KEY `idx_bxw_n8n_execution_task_type_id` (`task_type`, `task_id`),
  KEY `idx_bxw_n8n_execution_user_id` (`user_id`),
  KEY `idx_bxw_n8n_execution_status` (`status`),
  KEY `idx_bxw_n8n_execution_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='n8n执行记录表';

-- =====================================================
-- 11. 日志相关表
-- =====================================================

-- 操作日志表
DROP TABLE IF EXISTS `bxw_operation_log`;
CREATE TABLE `bxw_operation_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '用户ID',
  `admin_id` int(10) unsigned DEFAULT NULL COMMENT '管理员ID',
  `module` varchar(50) NOT NULL COMMENT '操作模块',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `description` varchar(500) NOT NULL COMMENT '操作描述',
  `request_data` text COMMENT '请求数据(JSON)',
  `response_data` text COMMENT '响应数据(JSON)',
  `ip_address` varchar(50) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execution_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '执行时间(毫秒)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_operation_log_user_id` (`user_id`),
  KEY `idx_bxw_operation_log_admin_id` (`admin_id`),
  KEY `idx_bxw_operation_log_module` (`module`),
  KEY `idx_bxw_operation_log_action` (`action`),
  KEY `idx_bxw_operation_log_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 错误日志表
DROP TABLE IF EXISTS `bxw_error_log`;
CREATE TABLE `bxw_error_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `level` varchar(20) NOT NULL COMMENT '错误级别(error/warning/info)',
  `category` varchar(50) NOT NULL COMMENT '错误分类',
  `message` varchar(1000) NOT NULL COMMENT '错误消息',
  `context` text COMMENT '错误上下文(JSON)',
  `stack_trace` text COMMENT '堆栈跟踪',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '用户ID',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bxw_error_log_level` (`level`),
  KEY `idx_bxw_error_log_category` (`category`),
  KEY `idx_bxw_error_log_user_id` (`user_id`),
  KEY `idx_bxw_error_log_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误日志表';

-- =====================================================
-- 12. 统计分析相关表
-- =====================================================

-- 用户行为统计表
DROP TABLE IF EXISTS `bxw_user_stats`;
CREATE TABLE `bxw_user_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `login_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `writing_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '写作次数',
  `rewrite_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '降重次数',
  `check_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查重次数',
  `export_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '导出次数',
  `credits_consumed` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消耗积分',
  `online_duration` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '在线时长(分钟)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_user_stats_user_date` (`user_id`, `date`),
  KEY `idx_bxw_user_stats_date` (`date`),
  KEY `idx_stats_date_user` (`date`, `user_id`),
  KEY `idx_user_stats_activity` (`user_id`, `date`, `login_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为统计表';

-- 系统统计表
DROP TABLE IF EXISTS `bxw_system_stats`;
CREATE TABLE `bxw_system_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `date` date NOT NULL COMMENT '统计日期',
  `new_users` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '新增用户',
  `active_users` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '活跃用户',
  `total_orders` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单总数',
  `total_revenue` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `writing_tasks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '写作任务',
  `rewrite_tasks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '降重任务',
  `check_tasks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查重任务',
  `ai_api_calls` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'AI调用次数',
  `ai_api_cost` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT 'AI调用成本',
  `error_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '错误数量',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bxw_system_stats_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统统计表';

-- =====================================================
-- 13. 初始化数据
-- =====================================================

-- 插入默认论文类型
INSERT INTO `bxw_paper_type` (`name`, `description`, `word_count_min`, `word_count_max`, `sort`) VALUES
('毕业论文', '本科、硕士、博士毕业论文', 8000, 50000, 1),
('学术论文', '期刊论文、会议论文', 3000, 15000, 2),
('综述论文', '文献综述、研究综述', 5000, 20000, 3),
('开题报告', '研究开题报告', 2000, 8000, 4),
('课程论文', '课程作业论文', 1000, 5000, 5);

-- 插入默认AI模型配置
INSERT INTO `bxw_ai_model` (`name`, `provider`, `model_code`, `api_endpoint`, `max_tokens`, `temperature`, `cost_per_1k_tokens`, `priority`) VALUES
('GPT-4', 'openai', 'gpt-4', 'https://api.openai.com/v1/chat/completions', 8000, 0.70, 0.030000, 100),
('GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', 'https://api.openai.com/v1/chat/completions', 4000, 0.70, 0.002000, 90),
('文心一言', 'baidu', 'ernie-bot', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions', 4000, 0.70, 0.001200, 80),
('通义千问', 'aliyun', 'qwen-max', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 6000, 0.70, 0.002000, 85);

-- 插入默认套餐配置
INSERT INTO `bxw_package` (`name`, `type`, `description`, `credits`, `writing_quota`, `rewrite_quota`, `check_quota`, `original_price`, `sale_price`, `sort`) VALUES
('体验套餐', 'credits', '新用户体验套餐，包含基础功能', 100, 2, 5, 1, 19.90, 9.90, 1),
('标准套餐', 'credits', '标准用户套餐，适合日常使用', 500, 10, 20, 5, 99.00, 79.00, 2),
('专业套餐', 'credits', '专业用户套餐，功能全面', 1000, 20, 50, 10, 199.00, 149.00, 3),
('VIP月卡', 'vip', 'VIP会员月卡，享受更多特权', 0, 0, 0, 0, 99.00, 89.00, 4),
('VIP年卡', 'vip', 'VIP会员年卡，超值优惠', 0, 0, 0, 0, 999.00, 699.00, 5);

-- 插入默认系统配置
INSERT INTO `bxw_config` (`group`, `name`, `title`, `value`, `type`, `description`, `sort`) VALUES
('system', 'site_name', '网站名称', 'AI论文写作平台', 'string', '网站名称配置', 1),
('system', 'site_description', '网站描述', '基于AI的智能论文写作平台', 'string', '网站描述配置', 2),
('system', 'default_credits', '新用户默认积分', '50', 'number', '新用户注册默认获得的积分', 3),
('ai', 'default_model', '默认AI模型', 'gpt-3.5-turbo', 'string', '系统默认使用的AI模型', 1),
('ai', 'max_concurrent_tasks', '最大并发任务数', '10', 'number', '系统最大并发处理任务数', 2),
('payment', 'wechat_enabled', '微信支付启用', 'true', 'boolean', '是否启用微信支付', 1),
('payment', 'alipay_enabled', '支付宝支付启用', 'true', 'boolean', '是否启用支付宝支付', 2),
('notification', 'email_enabled', '邮件通知启用', 'true', 'boolean', '是否启用邮件通知', 1),
('notification', 'sms_enabled', '短信通知启用', 'false', 'boolean', '是否启用短信通知', 2);

-- 插入默认消息模板
INSERT INTO `bxw_message_template` (`code`, `name`, `type`, `subject`, `content`, `variables`) VALUES
('task_completed', '任务完成通知', 'system', NULL, '您的{task_type}任务"{task_title}"已完成，请及时查看结果。', '{"task_type":"任务类型","task_title":"任务标题"}'),
('payment_success', '支付成功通知', 'system', NULL, '您的订单{order_no}支付成功，金额：￥{amount}，感谢您的支持！', '{"order_no":"订单号","amount":"支付金额"}'),
('welcome_email', '欢迎邮件', 'email', '欢迎使用AI论文写作平台', '亲爱的{username}，欢迎使用我们的AI论文写作平台！您已获得{credits}积分，可以开始体验我们的服务。', '{"username":"用户名","credits":"积分数量"}'),
('draft_auto_save', '草稿自动保存通知', 'system', NULL, '您的论文"{title}"草稿已自动保存，版本号：{version}', '{"title":"论文标题","version":"版本号"}'),
('invoice_issued', '发票开具通知', 'system', NULL, '您申请的发票已开具完成，发票号：{invoice_number}，请及时下载。', '{"invoice_number":"发票号码"}'),
('task_failed', '任务失败通知', 'system', NULL, '您的{task_type}任务"{task_title}"执行失败，错误信息：{error_message}', '{"task_type":"任务类型","task_title":"任务标题","error_message":"错误信息"}');

-- 插入默认n8n工作流配置
INSERT INTO `bxw_n8n_workflow` (`name`, `type`, `n8n_workflow_id`, `description`) VALUES
('论文大纲生成', 'outline', 'outline-generation-workflow', '根据用户输入生成论文大纲的工作流'),
('论文写作', 'writing', 'paper-writing-workflow', '根据大纲生成论文内容的工作流'),
('语义降重', 'rewrite', 'text-rewrite-workflow', '对文本进行语义降重的工作流'),
('论文查重', 'check', 'plagiarism-check-workflow', '提交论文进行查重检测的工作流'),
('文档导出', 'export', 'document-export-workflow', '将内容导出为DOCX或PDF的工作流');

-- =====================================================
-- 14. 外键约束配置
-- =====================================================

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 用户相关外键
ALTER TABLE `bxw_user_quota` ADD CONSTRAINT `fk_user_quota_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

-- 论文项目相关外键
ALTER TABLE `bxw_paper_project` ADD CONSTRAINT `fk_paper_project_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_paper_project` ADD CONSTRAINT `fk_paper_project_type`
    FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type`(`id`) ON DELETE RESTRICT;
ALTER TABLE `bxw_paper_project` ADD CONSTRAINT `fk_paper_project_parent`
    FOREIGN KEY (`parent_id`) REFERENCES `bxw_paper_project`(`id`) ON DELETE SET NULL;

ALTER TABLE `bxw_paper_section` ADD CONSTRAINT `fk_paper_section_project`
    FOREIGN KEY (`project_id`) REFERENCES `bxw_paper_project`(`id`) ON DELETE CASCADE;

-- 模板相关外键
ALTER TABLE `bxw_outline_template` ADD CONSTRAINT `fk_outline_template_paper_type`
    FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type`(`id`) ON DELETE SET NULL;
ALTER TABLE `bxw_prompt_template` ADD CONSTRAINT `fk_prompt_template_paper_type`
    FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type`(`id`) ON DELETE SET NULL;
ALTER TABLE `bxw_document_template` ADD CONSTRAINT `fk_document_template_paper_type`
    FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type`(`id`) ON DELETE SET NULL;

-- AI使用相关外键
ALTER TABLE `bxw_ai_usage_log` ADD CONSTRAINT `fk_ai_usage_log_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_ai_usage_log` ADD CONSTRAINT `fk_ai_usage_log_ai_model`
    FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model`(`id`) ON DELETE CASCADE;

-- 降重相关外键
ALTER TABLE `bxw_rewrite_task` ADD CONSTRAINT `fk_rewrite_task_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_rewrite_task` ADD CONSTRAINT `fk_rewrite_task_ai_model`
    FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model`(`id`) ON DELETE SET NULL;
ALTER TABLE `bxw_rewrite_result` ADD CONSTRAINT `fk_rewrite_result_task`
    FOREIGN KEY (`task_id`) REFERENCES `bxw_rewrite_task`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_rewrite_result` ADD CONSTRAINT `fk_rewrite_result_ai_model`
    FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model`(`id`) ON DELETE CASCADE;

-- 查重相关外键
ALTER TABLE `bxw_check_task` ADD CONSTRAINT `fk_check_task_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_check_task` ADD CONSTRAINT `fk_check_task_check_api`
    FOREIGN KEY (`check_api_id`) REFERENCES `bxw_check_api`(`id`) ON DELETE RESTRICT;
ALTER TABLE `bxw_check_detail` ADD CONSTRAINT `fk_check_detail_task`
    FOREIGN KEY (`task_id`) REFERENCES `bxw_check_task`(`id`) ON DELETE CASCADE;

-- 导出相关外键
ALTER TABLE `bxw_export_record` ADD CONSTRAINT `fk_export_record_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_export_record` ADD CONSTRAINT `fk_export_record_template`
    FOREIGN KEY (`template_id`) REFERENCES `bxw_document_template`(`id`) ON DELETE SET NULL;

-- 订单支付相关外键
ALTER TABLE `bxw_order` ADD CONSTRAINT `fk_order_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_order` ADD CONSTRAINT `fk_order_package`
    FOREIGN KEY (`package_id`) REFERENCES `bxw_package`(`id`) ON DELETE RESTRICT;
ALTER TABLE `bxw_order` ADD CONSTRAINT `fk_order_coupon`
    FOREIGN KEY (`coupon_id`) REFERENCES `bxw_coupon`(`id`) ON DELETE SET NULL;

ALTER TABLE `bxw_user_coupon` ADD CONSTRAINT `fk_user_coupon_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_user_coupon` ADD CONSTRAINT `fk_user_coupon_coupon`
    FOREIGN KEY (`coupon_id`) REFERENCES `bxw_coupon`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_user_coupon` ADD CONSTRAINT `fk_user_coupon_order`
    FOREIGN KEY (`order_id`) REFERENCES `bxw_order`(`id`) ON DELETE SET NULL;

ALTER TABLE `bxw_credits_log` ADD CONSTRAINT `fk_credits_log_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

ALTER TABLE `bxw_invoice` ADD CONSTRAINT `fk_invoice_order`
    FOREIGN KEY (`order_id`) REFERENCES `bxw_order`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_invoice` ADD CONSTRAINT `fk_invoice_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

-- 通知相关外键
ALTER TABLE `bxw_user_notification` ADD CONSTRAINT `fk_user_notification_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

ALTER TABLE `bxw_email_log` ADD CONSTRAINT `fk_email_log_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE SET NULL;
ALTER TABLE `bxw_email_log` ADD CONSTRAINT `fk_email_log_template`
    FOREIGN KEY (`template_id`) REFERENCES `bxw_message_template`(`id`) ON DELETE SET NULL;

-- n8n相关外键
ALTER TABLE `bxw_n8n_execution` ADD CONSTRAINT `fk_n8n_execution_workflow`
    FOREIGN KEY (`workflow_id`) REFERENCES `bxw_n8n_workflow`(`id`) ON DELETE CASCADE;
ALTER TABLE `bxw_n8n_execution` ADD CONSTRAINT `fk_n8n_execution_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

-- 统计相关外键
ALTER TABLE `bxw_user_stats` ADD CONSTRAINT `fk_user_stats_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE CASCADE;

-- 日志相关外键
ALTER TABLE `bxw_operation_log` ADD CONSTRAINT `fk_operation_log_user`
    FOREIGN KEY (`user_id`) REFERENCES `bxw_user`(`id`) ON DELETE SET NULL;

-- =====================================================
-- 15. 数据库优化建议
-- =====================================================

-- 创建复合索引以优化查询性能
CREATE INDEX `idx_paper_user_status_time` ON `bxw_paper_project` (`user_id`, `status`, `createtime`);
CREATE INDEX `idx_task_user_status_time` ON `bxw_rewrite_task` (`user_id`, `status`, `createtime`);
CREATE INDEX `idx_check_user_status_time` ON `bxw_check_task` (`user_id`, `status`, `createtime`);
CREATE INDEX `idx_order_user_status_time` ON `bxw_order` (`user_id`, `payment_status`, `createtime`);
CREATE INDEX `idx_notification_user_read_time` ON `bxw_user_notification` (`user_id`, `is_read`, `createtime`);

-- 创建分区表建议（可选，适用于大数据量场景）
-- ALTER TABLE `bxw_ai_usage_log` PARTITION BY RANGE (YEAR(createtime)) (
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- 16. 数据库初始化完成
-- =====================================================

-- 重置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 数据库初始化完成提示
SELECT 'AI论文写作平台数据库(MySQL版本)初始化完成！' AS message,
       '共创建表数量' AS info1, COUNT(*) AS table_count
FROM information_schema.tables
WHERE table_schema = DATABASE() AND table_name LIKE 'bxw_%';

-- 显示数据库版本信息
SELECT VERSION() AS mysql_version,
       DATABASE() AS database_name,
       NOW() AS init_time;
