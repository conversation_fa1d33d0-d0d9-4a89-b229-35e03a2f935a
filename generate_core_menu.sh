#!/bin/bash
# AI论文写作平台 - 核心菜单生成脚本
# 创建时间: 2025-01-03
# 说明: 为核心控制器生成后台权限菜单

echo "=========================================="
echo "AI论文写作平台 - 核心菜单生成"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

echo "开始为AI论文写作平台核心模块生成后台权限菜单..."
echo "本脚本将生成最重要的业务模块菜单，适合快速搭建原型"

# 1. 用户模块菜单（基础依赖）
echo ""
echo "👤 生成用户模块菜单..."
php think menu -c user

# 2. AI模型配置菜单（AI功能依赖）
echo ""
echo "🧠 生成AI模型配置菜单..."
php think menu -c aimodel

# 3. 论文类型菜单（写作功能依赖）
echo ""
echo "📄 生成论文类型管理菜单..."
php think menu -c papertype

# 4. 写作任务菜单（核心业务）
echo ""
echo "📝 生成写作任务管理菜单..."
php think menu -c paperproject

# 5. 降重任务菜单（核心功能）
echo ""
echo "🔄 生成降重任务管理菜单..."
php think menu -c rewritetask

# 6. 查重接口配置菜单
echo ""
echo "🔍 生成查重接口配置菜单..."
php think menu -c checkapi

# 7. 查重任务菜单
echo ""
echo "📊 生成查重任务管理菜单..."
php think menu -c checktask

# 8. VIP套餐管理菜单（收费功能）
echo ""
echo "💰 生成VIP套餐管理菜单..."
php think menu -c package

# 9. 订单管理菜单（收费功能）
echo ""
echo "🛒 生成订单管理菜单..."
php think menu -c order

# 10. 用户积分管理菜单
echo ""
echo "💎 生成用户积分管理菜单..."
php think menu -c creditslog

echo ""
echo "=========================================="
echo "🎉 核心菜单生成完成！"
echo "=========================================="
echo ""
echo "已生成的核心菜单:"
echo "✅ 用户管理 - 用户基础信息管理"
echo "✅ AI模型配置 - AI服务配置管理"
echo "✅ 论文类型 - 论文分类管理"
echo "✅ 写作任务 - 论文写作核心功能"
echo "✅ 降重任务 - 语义降重功能"
echo "✅ 查重接口 - 第三方查重服务配置"
echo "✅ 查重任务 - 论文查重功能"
echo "✅ VIP套餐 - 会员套餐管理"
echo "✅ 订单管理 - 支付订单管理"
echo "✅ 积分管理 - 用户积分流水"
echo ""
echo "下一步操作:"
echo "1. 登录后台查看生成的菜单结构"
echo "2. 调整菜单显示名称和图标"
echo "3. 设置菜单权限和角色分配"
echo "4. 如需完整菜单，运行: ./generate_menu.sh"
echo ""
echo "菜单管理路径: 后台 → 权限管理 → 菜单规则"
echo "核心业务流程:"
echo "用户注册 → 购买套餐 → 创建写作任务 → AI生成内容 → 降重优化 → 查重检测"
echo "=========================================="
