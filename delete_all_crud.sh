#!/bin/bash
# AI论文写作平台 - 批量删除CRUD脚本
# 创建时间: 2025-01-03
# 说明: 一键删除所有模块的FastAdmin CRUD

echo "=========================================="
echo "AI论文写作平台 - 批量删除CRUD"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "think" ]; then
    echo "错误: 请在FastAdmin项目根目录下运行此脚本"
    exit 1
fi

# 确认删除操作
echo "⚠️  警告: 此操作将删除所有已生成的CRUD文件！"
echo "包括控制器、模型、视图、JS文件、语言包等"
echo ""
read -p "确认要继续删除吗？(y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始删除AI论文写作平台所有CRUD..."

# 写作中心模块
echo ""
echo "📄 删除写作中心模块..."
echo "删除论文类型管理..."
php think crud -t bxw_paper_type -c papertype -d 1

echo "删除大纲模板管理..."
php think crud -t bxw_outline_template -c outlinetemplate -d 1

echo "删除提示词模板管理..."
php think crud -t bxw_prompt_template -c prompttemplate -d 1

echo "删除写作任务管理..."
php think crud -t bxw_paper_project -c paperproject -d 1

echo "删除草稿箱管理..."
php think crud -t bxw_paper_project -c draftbox -d 1

# 降重与查重模块
echo ""
echo "🔄 删除降重与查重模块..."
echo "删除降重任务管理..."
php think crud -t bxw_rewrite_task -c rewritetask -d 1

echo "删除降重结果管理..."
php think crud -t bxw_rewrite_result -c rewriteresult -d 1

echo "删除查重任务管理..."
php think crud -t bxw_check_task -c checktask -d 1

echo "删除查重接口配置..."
php think crud -t bxw_check_api -c checkapi -d 1

# 文档导出模块
echo ""
echo "📁 删除文档导出模块..."
echo "删除导出样式模板..."
php think crud -t bxw_document_template -c documenttemplate -d 1

echo "删除下载记录管理..."
php think crud -t bxw_export_record -c exportrecord -d 1

# 用户中心模块
echo ""
echo "👤 删除用户中心模块..."
echo "删除用户列表..."
php think crud -t bxw_user -c user -d 1

echo "删除用户配额管理..."
php think crud -t bxw_user_quota -c userquota -d 1

echo "删除VIP套餐管理..."
php think crud -t bxw_package -c package -d 1

echo "删除用户积分管理..."
php think crud -t bxw_credits_log -c creditslog -d 1

# 收费系统模块
echo ""
echo "💰 删除收费系统模块..."
echo "删除订单管理..."
php think crud -t bxw_order -c order -d 1

echo "删除优惠券管理..."
php think crud -t bxw_coupon -c coupon -d 1

echo "删除发票管理..."
php think crud -t bxw_invoice -c invoice -d 1

# 通知与消息模块
echo ""
echo "📬 删除通知与消息模块..."
echo "删除消息模板管理..."
php think crud -t bxw_message_template -c messagetemplate -d 1

echo "删除用户通知记录..."
php think crud -t bxw_user_notification -c usernotification -d 1

echo "删除邮件发送记录..."
php think crud -t bxw_email_log -c emaillog -d 1

# 系统设置模块
echo ""
echo "🧠 删除系统设置模块..."
echo "删除AI模型配置..."
php think crud -t bxw_ai_model -c aimodel -d 1

echo "删除系统配置管理..."
php think crud -t bxw_config -c config -d 1

echo "删除内容风控规则..."
php think crud -t bxw_content_filter -c contentfilter -d 1

echo "删除n8n工作流配置..."
php think crud -t bxw_n8n_workflow -c n8nworkflow -d 1

echo "删除n8n执行记录..."
php think crud -t bxw_n8n_execution -c n8nexecution -d 1

# 日志统计模块
echo ""
echo "📊 删除日志统计模块..."
echo "删除操作日志..."
php think crud -t bxw_operation_log -c operationlog -d 1

echo "删除错误日志..."
php think crud -t bxw_error_log -c errorlog -d 1

echo "删除用户行为统计..."
php think crud -t bxw_user_stats -c userstats -d 1

echo "删除系统统计..."
php think crud -t bxw_system_stats -c systemstats -d 1

echo "删除AI使用统计..."
php think crud -t bxw_ai_usage_log -c aiusagelog -d 1

echo ""
echo "=========================================="
echo "🗑️  所有CRUD删除完成！"
echo "=========================================="
echo ""
echo "删除统计:"
echo "- 📄 写作中心模块: 5个CRUD已删除"
echo "- 🔄 降重查重模块: 4个CRUD已删除"
echo "- 📁 文档导出模块: 2个CRUD已删除"
echo "- 👤 用户中心模块: 4个CRUD已删除"
echo "- 💰 收费系统模块: 3个CRUD已删除"
echo "- 📬 通知消息模块: 3个CRUD已删除"
echo "- 🧠 系统设置模块: 5个CRUD已删除"
echo "- 📊 日志统计模块: 5个CRUD已删除"
echo "- 总计: 31个CRUD已删除"
echo ""
echo "注意事项:"
echo "1. 相关菜单记录可能需要手动清理"
echo "2. 如需重新生成，请运行: ./generate_all_crud.sh"
echo "3. 建议清理浏览器缓存后重新访问后台"
echo "=========================================="
